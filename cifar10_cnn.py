import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
import numpy as np
import matplotlib.pyplot as plt
import os
from PIL import Image
import pandas as pd

# Set random seeds for reproducibility
tf.random.set_seed(42)
np.random.seed(42)

class CIFAR10CNN:
    def __init__(self, data_dir='cifar-10'):
        self.data_dir = data_dir
        self.num_classes = 10
        self.img_height = 32
        self.img_width = 32
        self.batch_size = 32
        
        # CIFAR-10 class names
        self.class_names = ['airplane', 'automobile', 'bird', 'cat', 'deer', 
                           'dog', 'frog', 'horse', 'ship', 'truck']
        
        self.model = None
        self.history = None
    
    def load_data(self):
        """Load and preprocess CIFAR-10 data from local files"""
        print("Loading CIFAR-10 data...")
        
        # Load training labels
        train_labels_df = pd.read_csv(os.path.join(self.data_dir, 'trainLabels.csv'))
        
        # Create dictionaries for quick lookup
        id_to_label = dict(zip(train_labels_df['id'], train_labels_df['label']))
        
        # Load training images
        train_dir = os.path.join(self.data_dir, 'train')
        train_images = []
        train_labels = []
        
        print("Loading training images...")
        for filename in os.listdir(train_dir):
            if filename.endswith('.png'):
                img_id = int(filename.split('.')[0])
                img_path = os.path.join(train_dir, filename)
                
                # Load and preprocess image
                img = Image.open(img_path)
                img_array = np.array(img)
                train_images.append(img_array)
                
                # Get corresponding label
                label = id_to_label[img_id]
                train_labels.append(self.class_names.index(label))
        
        # Convert to numpy arrays
        train_images = np.array(train_images)
        train_labels = np.array(train_labels)
        
        # Normalize pixel values to [0, 1]
        train_images = train_images.astype('float32') / 255.0
        
        # Split into train and validation sets
        split_idx = int(0.8 * len(train_images))
        
        x_train = train_images[:split_idx]
        y_train = train_labels[:split_idx]
        x_val = train_images[split_idx:]
        y_val = train_labels[split_idx:]
        
        print(f"Training set: {x_train.shape[0]} images")
        print(f"Validation set: {x_val.shape[0]} images")
        print(f"Image shape: {x_train.shape[1:]}")
        
        return (x_train, y_train), (x_val, y_val)
    
    def build_model(self):
        """Build a simple CNN model"""
        print("Building CNN model...")
        
        model = keras.Sequential([
            # First convolutional block
            layers.Conv2D(32, (3, 3), activation='relu', input_shape=(32, 32, 3)),
            layers.BatchNormalization(),
            layers.Conv2D(32, (3, 3), activation='relu'),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Second convolutional block
            layers.Conv2D(64, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.Conv2D(64, (3, 3), activation='relu'),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Third convolutional block
            layers.Conv2D(128, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.25),
            
            # Flatten and dense layers
            layers.Flatten(),
            layers.Dense(512, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            layers.Dense(self.num_classes, activation='softmax')
        ])
        
        # Compile the model
        model.compile(
            optimizer='adam',
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        self.model = model
        return model
    
    def train(self, epochs=20):
        """Train the model"""
        if self.model is None:
            raise ValueError("Model not built. Call build_model() first.")
        
        # Load data
        (x_train, y_train), (x_val, y_val) = self.load_data()
        
        # Data augmentation
        data_augmentation = keras.Sequential([
            layers.RandomFlip("horizontal"),
            layers.RandomRotation(0.1),
            layers.RandomZoom(0.1),
        ])
        
        # Apply data augmentation to training data
        augmented_train_ds = tf.data.Dataset.from_tensor_slices((x_train, y_train))
        augmented_train_ds = augmented_train_ds.batch(self.batch_size)
        augmented_train_ds = augmented_train_ds.map(
            lambda x, y: (data_augmentation(x, training=True), y),
            num_parallel_calls=tf.data.AUTOTUNE
        )
        augmented_train_ds = augmented_train_ds.prefetch(tf.data.AUTOTUNE)
        
        # Validation dataset
        val_ds = tf.data.Dataset.from_tensor_slices((x_val, y_val))
        val_ds = val_ds.batch(self.batch_size).prefetch(tf.data.AUTOTUNE)
        
        # Callbacks
        callbacks = [
            keras.callbacks.EarlyStopping(patience=5, restore_best_weights=True),
            keras.callbacks.ReduceLROnPlateau(factor=0.2, patience=3)
        ]
        
        print("Starting training...")
        self.history = self.model.fit(
            augmented_train_ds,
            epochs=epochs,
            validation_data=val_ds,
            callbacks=callbacks,
            verbose=1
        )
        
        return self.history
    
    def plot_training_history(self):
        """Plot training history"""
        if self.history is None:
            print("No training history available. Train the model first.")
            return
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # Plot accuracy
        ax1.plot(self.history.history['accuracy'], label='Training Accuracy')
        ax1.plot(self.history.history['val_accuracy'], label='Validation Accuracy')
        ax1.set_title('Model Accuracy')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Accuracy')
        ax1.legend()
        ax1.grid(True)
        
        # Plot loss
        ax2.plot(self.history.history['loss'], label='Training Loss')
        ax2.plot(self.history.history['val_loss'], label='Validation Loss')
        ax2.set_title('Model Loss')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Loss')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig('training_history.png', dpi=150, bbox_inches='tight')
        plt.show()
    
    def evaluate(self):
        """Evaluate the model on validation data"""
        if self.model is None:
            print("Model not trained. Train the model first.")
            return
        
        (x_train, y_train), (x_val, y_val) = self.load_data()
        
        # Evaluate on validation set
        val_loss, val_accuracy = self.model.evaluate(x_val, y_val, verbose=0)
        print(f"\nValidation Accuracy: {val_accuracy:.4f}")
        print(f"Validation Loss: {val_loss:.4f}")
        
        return val_loss, val_accuracy
    
    def save_model(self, filepath='cifar10_cnn_model.h5'):
        """Save the trained model"""
        if self.model is None:
            print("No model to save. Train the model first.")
            return
        
        self.model.save(filepath)
        print(f"Model saved to {filepath}")
    
    def load_model(self, filepath='cifar10_cnn_model.h5'):
        """Load a saved model"""
        self.model = keras.models.load_model(filepath)
        print(f"Model loaded from {filepath}")
    
    def predict_sample(self, num_samples=5):
        """Make predictions on a few sample images"""
        if self.model is None:
            print("Model not trained. Train the model first.")
            return
        
        (x_train, y_train), (x_val, y_val) = self.load_data()
        
        # Get random samples from validation set
        indices = np.random.choice(len(x_val), num_samples, replace=False)
        sample_images = x_val[indices]
        sample_labels = y_val[indices]
        
        # Make predictions
        predictions = self.model.predict(sample_images)
        predicted_classes = np.argmax(predictions, axis=1)
        
        # Display results
        fig, axes = plt.subplots(1, num_samples, figsize=(15, 3))
        for i in range(num_samples):
            axes[i].imshow(sample_images[i])
            axes[i].set_title(f'True: {self.class_names[sample_labels[i]]}\n'
                            f'Pred: {self.class_names[predicted_classes[i]]}')
            axes[i].axis('off')
        
        plt.tight_layout()
        plt.savefig('predictions.png', dpi=150, bbox_inches='tight')
        plt.show()


def main():
    """Main function to run the CIFAR-10 CNN training"""
    print("CIFAR-10 CNN Training Script")
    print("=" * 40)
    
    # Initialize the CNN
    cnn = CIFAR10CNN()
    
    # Build the model
    model = cnn.build_model()
    
    # Print model summary
    print("\nModel Architecture:")
    model.summary()
    
    # Train the model
    print("\nStarting training...")
    history = cnn.train(epochs=20)
    
    # Evaluate the model
    print("\nEvaluating model...")
    cnn.evaluate()
    
    # Plot training history
    print("\nPlotting training history...")
    cnn.plot_training_history()
    
    # Make sample predictions
    print("\nMaking sample predictions...")
    cnn.predict_sample()
    
    # Save the model
    print("\nSaving model...")
    cnn.save_model()
    
    print("\nTraining completed successfully!")


if __name__ == "__main__":
    main()
