# CIFAR-10 Object Recognition with CNN

This project implements a Convolutional Neural Network (CNN) for image classification on the CIFAR-10 dataset using TensorFlow/Keras.

## Overview

CIFAR-10 is a well-known computer vision dataset consisting of 60,000 32x32 color images in 10 classes, with 6,000 images per class. The classes are: airplane, automobile, bird, cat, deer, dog, frog, horse, ship, and truck.

## Project Structure

```
Object_recognition/
├── cifar-10/                 # CIFAR-10 dataset directory
│   ├── train/               # Training images
│   ├── test/                # Test images
│   └── trainLabels.csv      # Training labels
├── cifar10_cnn.py          # Main CNN implementation
├── requirements.txt         # Python dependencies
└── README.md               # This file
```

## Methods and Architecture

### 1. Data Preprocessing

**Image Loading and Normalization**
- Images are loaded from PNG files using PIL (Python Imaging Library)
- Pixel values are normalized from [0, 255] to [0, 1] range for better training stability
- Data is split into 80% training and 20% validation sets

**Data Augmentation**
- Random horizontal flips to increase dataset diversity
- Random rotations (±10%) to improve model robustness
- Random zoom (±10%) to handle scale variations

### 2. CNN Architecture

The model follows a hierarchical feature extraction approach:

**Convolutional Blocks:**
1. **First Block**: 32 filters, 3x3 kernels
   - Conv2D(32) → BatchNorm → Conv2D(32) → MaxPool2D → Dropout(0.25)
   
2. **Second Block**: 64 filters, 3x3 kernels
   - Conv2D(64) → BatchNorm → Conv2D(64) → MaxPool2D → Dropout(0.25)
   
3. **Third Block**: 128 filters, 3x3 kernels
   - Conv2D(128) → BatchNorm → Dropout(0.25)

**Classification Head:**
- Flatten layer to convert 2D feature maps to 1D
- Dense layer with 512 neurons and ReLU activation
- Batch normalization for training stability
- Dropout(0.5) for regularization
- Output layer with 10 neurons (softmax activation)

### 3. Training Process

**Optimization Strategy:**
- **Optimizer**: Adam (adaptive learning rate)
- **Loss Function**: Sparse categorical crossentropy
- **Metrics**: Accuracy

**Regularization Techniques:**
- **Batch Normalization**: Normalizes inputs to each layer
- **Dropout**: Prevents overfitting by randomly setting neurons to zero
- **Early Stopping**: Stops training when validation loss stops improving
- **Learning Rate Reduction**: Reduces learning rate when loss plateaus

**Training Configuration:**
- Batch size: 32 images
- Initial epochs: 20 (with early stopping)
- Validation split: 20% of training data

### 4. Model Evaluation

**Performance Metrics:**
- Training and validation accuracy
- Training and validation loss
- Visual inspection of predictions vs ground truth

**Visualization:**
- Training history plots (accuracy and loss curves)
- Sample predictions with confidence scores
- Confusion analysis through sample predictions

## Installation and Usage

### Prerequisites

```bash
pip install -r requirements.txt
```

### Running the Model

```bash
python cifar10_cnn.py
```

### Expected Output

The script will:
1. Load and preprocess the CIFAR-10 dataset
2. Build the CNN architecture
3. Train the model with data augmentation
4. Display training progress and metrics
5. Generate visualization plots
6. Save the trained model

## Expected Performance

- **Target Accuracy**: 70-80% on validation set
- **Training Time**: 10-30 minutes (depending on hardware)
- **Model Size**: ~2-5 MB

## Technical Details

### Key Design Decisions

1. **Progressive Filter Increase**: Filters increase from 32 → 64 → 128 to capture increasingly complex features
2. **Batch Normalization**: Applied after each convolutional layer to stabilize training
3. **Dropout Regularization**: Higher dropout (0.5) in dense layers, lower (0.25) in conv layers
4. **Data Augmentation**: Applied only during training to prevent overfitting

### Computational Complexity

- **Parameters**: ~1.2M trainable parameters
- **FLOPs**: ~50M floating-point operations per forward pass
- **Memory**: ~100MB GPU memory required for training

## File Descriptions

### `cifar10_cnn.py`
Main implementation containing:
- `CIFAR10CNN` class with all functionality
- Data loading and preprocessing methods
- Model architecture definition
- Training loop with callbacks
- Evaluation and visualization functions

### Key Methods:

- `load_data()`: Loads images and labels from local files
- `build_model()`: Constructs the CNN architecture
- `train()`: Trains the model with augmentation and callbacks
- `evaluate()`: Evaluates model performance
- `plot_training_history()`: Visualizes training metrics
- `predict_sample()`: Shows sample predictions

## Customization Options

### Hyperparameter Tuning
- Modify `batch_size`, `epochs` in the class initialization
- Adjust learning rate in the optimizer
- Change dropout rates for different regularization levels

### Architecture Modifications
- Add more convolutional layers for deeper networks
- Experiment with different filter sizes (5x5, 7x7)
- Try different activation functions (LeakyReLU, ELU)

### Data Augmentation
- Add more augmentation techniques (brightness, contrast)
- Adjust augmentation parameters for stronger/weaker effects

## Troubleshooting

**Common Issues:**
1. **Memory Errors**: Reduce batch size or use mixed precision training
2. **Slow Training**: Enable GPU acceleration or reduce model complexity
3. **Poor Accuracy**: Increase training epochs or adjust learning rate

**Performance Tips:**
- Use GPU acceleration for faster training
- Monitor validation loss to detect overfitting
- Experiment with different optimizers (SGD, RMSprop)

## Future Improvements

- Implement transfer learning with pre-trained models
- Add test set evaluation functionality
- Implement ensemble methods for better accuracy
- Add support for other datasets (CIFAR-100, ImageNet)
- Implement advanced architectures (ResNet, DenseNet)

## References

- CIFAR-10 Dataset: https://www.cs.toronto.edu/~kriz/cifar.html
- TensorFlow Documentation: https://www.tensorflow.org/
- Keras API Reference: https://keras.io/
